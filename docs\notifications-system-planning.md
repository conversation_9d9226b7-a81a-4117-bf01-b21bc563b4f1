# Sistema de Notificações - Planejamento Detalhado

## Visão Geral

Este documento detalha o planejamento para implementação de um sistema completo de notificações para o SaaS de gerenciamento de academias de artes marciais. O sistema suportará três canais principais:

1. **In-App** (Prioridade Alta) - Notificações dentro da plataforma
2. **E-mail** (Prioridade Média) - Resend → AWS SES
3. **WhatsApp** (Prioridade Baixa) - Evolution API

## Estado Atual da Aplicação

### Componentes Existentes
- ✅ `notifications-popover.tsx` - Componente de notificações no header (dados mockados)
- ✅ `notification-service.ts` - Serviço de notificações para billing (preparado para integração)
- ✅ `toast-notification.tsx` - Sistema de toasts temporários
- ❌ Tabelas de notificações no banco de dados
- ❌ Sistema de preferências de notificação
- ❌ Integração com provedores externos

### Arquitetura Multitenancy
- Cada academia (tenant) terá configurações independentes
- Domínios de e-mail: `<EMAIL>`
- Instâncias separadas de WhatsApp por academia
- Isolamento completo de dados entre tenants

## Fase 1: Sistema In-App (Prioridade Alta)

### 1.1 Estrutura do Banco de Dados

#### Tabela `notifications`
```sql
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL, -- 'payment', 'class', 'system', 'enrollment', 'event'
  category VARCHAR(50) NOT NULL, -- 'reminder', 'alert', 'info', 'success', 'error'
  priority VARCHAR(20) NOT NULL DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
  title VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}', -- Dados específicos da notificação
  status VARCHAR(20) NOT NULL DEFAULT 'unread', -- 'unread', 'read', 'archived', 'deleted'
  channels VARCHAR[] DEFAULT ARRAY['in_app'], -- Canais onde foi/será enviada
  scheduled_for TIMESTAMP WITH TIME ZONE, -- Para notificações agendadas
  expires_at TIMESTAMP WITH TIME ZONE, -- Data de expiração
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  read_at TIMESTAMP WITH TIME ZONE,
  
  -- Índices para performance
  INDEX idx_notifications_tenant_user (tenant_id, user_id),
  INDEX idx_notifications_status (status),
  INDEX idx_notifications_type_category (type, category),
  INDEX idx_notifications_created_at (created_at DESC),
  INDEX idx_notifications_scheduled (scheduled_for) WHERE scheduled_for IS NOT NULL
);
```

#### Tabela `notification_preferences`
```sql
CREATE TABLE notification_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notification_type VARCHAR(50) NOT NULL,
  in_app_enabled BOOLEAN DEFAULT true,
  email_enabled BOOLEAN DEFAULT true,
  whatsapp_enabled BOOLEAN DEFAULT false,
  quiet_hours_start TIME,
  quiet_hours_end TIME,
  timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(tenant_id, user_id, notification_type)
);
```

#### Tabela `notification_templates`
```sql
CREATE TABLE notification_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE, -- NULL para templates globais
  type VARCHAR(50) NOT NULL,
  channel VARCHAR(20) NOT NULL, -- 'in_app', 'email', 'whatsapp'
  name VARCHAR(100) NOT NULL,
  subject_template TEXT, -- Para e-mail
  body_template TEXT NOT NULL,
  variables JSONB DEFAULT '{}', -- Variáveis disponíveis no template
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE,
  
  UNIQUE(tenant_id, type, channel, name)
);
```

### 1.2 Tipos TypeScript

```typescript
// src/services/notifications/types/notification-types.ts
export interface Notification {
  id: string;
  tenant_id: string;
  user_id: string;
  type: NotificationType;
  category: NotificationCategory;
  priority: NotificationPriority;
  title: string;
  message: string;
  data: Record<string, any>;
  status: NotificationStatus;
  channels: NotificationChannel[];
  scheduled_for?: string;
  expires_at?: string;
  created_at: string;
  updated_at?: string;
  read_at?: string;
}

export type NotificationType = 
  | 'payment' 
  | 'class' 
  | 'system' 
  | 'enrollment' 
  | 'event';

export type NotificationCategory = 
  | 'reminder' 
  | 'alert' 
  | 'info' 
  | 'success' 
  | 'error';

export type NotificationPriority = 
  | 'low' 
  | 'medium' 
  | 'high' 
  | 'urgent';

export type NotificationStatus = 
  | 'unread' 
  | 'read' 
  | 'archived' 
  | 'deleted';

export type NotificationChannel = 
  | 'in_app' 
  | 'email' 
  | 'whatsapp';

export interface NotificationPreferences {
  id: string;
  tenant_id: string;
  user_id: string;
  notification_type: NotificationType;
  in_app_enabled: boolean;
  email_enabled: boolean;
  whatsapp_enabled: boolean;
  quiet_hours_start?: string;
  quiet_hours_end?: string;
  timezone: string;
  created_at: string;
  updated_at?: string;
}

export interface CreateNotificationData {
  user_id: string;
  type: NotificationType;
  category: NotificationCategory;
  priority?: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  channels?: NotificationChannel[];
  scheduled_for?: string;
  expires_at?: string;
}
```

### 1.3 Serviços Core

#### NotificationService Principal
```typescript
// src/services/notifications/core/notification-service.ts
export class NotificationService {
  async create(tenantId: string, data: CreateNotificationData): Promise<Notification>
  async getByUser(userId: string, filters?: NotificationFilters): Promise<PaginatedNotifications>
  async markAsRead(notificationId: string, userId: string): Promise<void>
  async markAllAsRead(userId: string): Promise<void>
  async archive(notificationId: string, userId: string): Promise<void>
  async delete(notificationId: string, userId: string): Promise<void>
  async getUnreadCount(userId: string): Promise<number>
  async scheduleNotification(tenantId: string, data: CreateNotificationData): Promise<void>
}
```

### 1.4 Hooks React

```typescript
// src/hooks/notifications/use-notifications.ts
export function useNotifications(filters?: NotificationFilters) {
  // Lista paginada de notificações
  // Real-time updates via Supabase subscriptions
}

export function useNotificationCount() {
  // Contador de notificações não lidas
  // Real-time updates
}

export function useNotificationPreferences() {
  // Gerenciamento de preferências do usuário
}
```

### 1.5 Componentes React

#### Lista de Notificações
```typescript
// src/components/notifications/notifications-list.tsx
export function NotificationsList({ 
  filters, 
  onNotificationClick, 
  showActions = true 
}: NotificationsListProps)
```

#### Item de Notificação
```typescript
// src/components/notifications/notification-item.tsx
export function NotificationItem({ 
  notification, 
  onClick, 
  showActions = true 
}: NotificationItemProps)
```

### 1.6 Página de Notificações

```typescript
// src/app/(dashboard)/notificacoes/page.tsx
export default function NotificationsPage() {
  // Página completa com filtros, busca, ações em lote
  // Tabs: Todas, Não lidas, Arquivadas
  // Filtros: Tipo, Categoria, Período
}
```

## Fase 2: Sistema de E-mail (Prioridade Média)

### 2.1 Arquitetura de Provedores

#### Interface Base
```typescript
// src/services/notifications/channels/email/providers/email-provider.ts
export interface EmailProvider {
  send(email: EmailData): Promise<EmailResult>;
  sendBatch(emails: EmailData[]): Promise<BatchEmailResult>;
  getDeliveryStatus(messageId: string): Promise<DeliveryStatus>;
  verifyDomain(domain: string): Promise<DomainVerification>;
}
```

#### Implementação Resend
```typescript
// src/services/notifications/channels/email/providers/resend-provider.ts
export class ResendProvider implements EmailProvider {
  constructor(private apiKey: string) {}
  
  async send(email: EmailData): Promise<EmailResult> {
    // Implementação com Resend SDK
  }
  
  // Outros métodos...
}
```

### 2.2 Sistema de Templates

#### Engine de Templates
```typescript
// src/services/notifications/templates/template-engine.ts
export class TemplateEngine {
  async renderEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail>
  async renderWhatsApp(templateId: string, variables: Record<string, any>): Promise<string>
}
```

#### Templates React Email
```typescript
// src/services/notifications/channels/email/templates/payment-reminder.tsx
export function PaymentReminderTemplate({ 
  studentName, 
  amount, 
  dueDate, 
  academyName,
  academyLogo 
}: PaymentReminderProps) {
  // Template responsivo com React Email
}
```

### 2.3 Configuração por Tenant

```typescript
// Tabela tenant_notification_settings
CREATE TABLE tenant_notification_settings (
  tenant_id UUID PRIMARY KEY REFERENCES tenants(id) ON DELETE CASCADE,
  email_provider VARCHAR(20) DEFAULT 'resend', -- 'resend', 'aws_ses'
  email_from_domain VARCHAR(100), -- '<EMAIL>'
  email_from_name VARCHAR(100),
  resend_api_key TEXT,
  aws_access_key TEXT,
  aws_secret_key TEXT,
  aws_region VARCHAR(20) DEFAULT 'us-east-1',
  whatsapp_instance_id VARCHAR(100),
  whatsapp_api_url TEXT,
  whatsapp_api_key TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE
);
```

## Fase 3: Sistema WhatsApp (Prioridade Baixa)

### 3.1 Integração Evolution API

#### Cliente Evolution API
```typescript
// src/services/notifications/channels/whatsapp/evolution-api-client.ts
export class EvolutionApiClient {
  async sendMessage(instanceId: string, message: WhatsAppMessage): Promise<MessageResult>
  async getInstanceStatus(instanceId: string): Promise<InstanceStatus>
  async validateNumber(number: string): Promise<NumberValidation>
  async setupWebhook(instanceId: string, webhookUrl: string): Promise<void>
}
```

### 3.2 Webhook Handling

```typescript
// src/app/api/webhooks/whatsapp/route.ts
export async function POST(request: Request) {
  // Processar webhooks da Evolution API
  // Status de entrega, leitura, etc.
}
```

### 3.3 Compliance e Opt-in

```typescript
// Tabela whatsapp_contacts
CREATE TABLE whatsapp_contacts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  phone_number VARCHAR(20) NOT NULL,
  opted_in BOOLEAN DEFAULT false,
  opted_in_at TIMESTAMP WITH TIME ZONE,
  opted_out_at TIMESTAMP WITH TIME ZONE,
  last_message_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(tenant_id, user_id)
);
```

## Estrutura de Arquivos

```
src/services/notifications/
├── core/
│   ├── notification-service.ts
│   ├── notification-queue.ts
│   └── notification-analytics.ts
├── channels/
│   ├── base/
│   │   └── notification-channel.ts
│   ├── in-app/
│   │   ├── in-app-channel.ts
│   │   └── in-app-service.ts
│   ├── email/
│   │   ├── email-channel.ts
│   │   ├── providers/
│   │   │   ├── email-provider.ts
│   │   │   ├── resend-provider.ts
│   │   │   └── aws-provider.ts
│   │   └── templates/
│   └── whatsapp/
│       ├── whatsapp-channel.ts
│       ├── evolution-api-client.ts
│       └── templates/
├── templates/
│   ├── template-engine.ts
│   └── types.ts
├── types/
│   └── notification-types.ts
└── index.ts

src/components/notifications/
├── notifications-list.tsx
├── notification-item.tsx
├── notification-preferences.tsx
└── notification-analytics.tsx

src/hooks/notifications/
├── use-notifications.ts
├── use-notification-count.ts
└── use-notification-preferences.ts
```

## Cronograma de Implementação

### Sprint 1 (1-2 semanas) - Base In-App
- [ ] Criação das tabelas no banco
- [ ] Tipos TypeScript e schemas Zod
- [ ] NotificationService básico
- [ ] Hooks React básicos

### Sprint 2 (1-2 semanas) - Interface In-App
- [ ] Atualização do notifications-popover
- [ ] Página de notificações
- [ ] Sistema de preferências
- [ ] Real-time updates

### Sprint 3 (2-3 semanas) - Sistema E-mail
- [ ] Abstração de provedores
- [ ] Implementação Resend
- [ ] Templates React Email
- [ ] Configuração por tenant

### Sprint 4 (2-3 semanas) - Sistema WhatsApp
- [ ] Integração Evolution API
- [ ] Templates WhatsApp
- [ ] Sistema de opt-in/opt-out
- [ ] Webhook handling

### Sprint 5 (1 semana) - Polimento
- [ ] Testes completos
- [ ] Documentação
- [ ] Analytics e monitoramento
- [ ] Performance optimization

## Considerações Técnicas

### Performance
- Paginação para listas grandes
- Cache Redis para notificações frequentes
- Índices otimizados no banco
- Lazy loading de componentes

### Segurança
- RLS no Supabase para isolamento
- Validação de permissões
- Sanitização de templates
- Rate limiting por tenant

### Escalabilidade
- Processamento assíncrono
- Filas para envios em lote
- Retry logic para falhas
- Monitoramento de performance

### Experiência do Usuário
- Real-time updates
- Modo offline com sincronização
- Acessibilidade completa
- Interface responsiva

## Próximos Passos

1. **Aprovação do Planejamento** - Revisar e aprovar este documento
2. **Setup do Ambiente** - Configurar dependências (Resend, etc.)
3. **Implementação Fase 1** - Começar com sistema In-App
4. **Testes e Validação** - Testar cada fase antes de prosseguir
5. **Deploy Gradual** - Implementar feature flags para rollout controlado

---

**Documento criado em:** 2025-01-26  
**Versão:** 1.0  
**Status:** Aguardando Aprovação
